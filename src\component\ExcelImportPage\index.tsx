import React, {FC, useState, useRef} from 'react';
import {toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import * as XLSX from 'xlsx';
import './index.scss';
import {formItems} from '@/utils/amisEditorItems/formitems';
import {
  createApplicationPageAndClass,
  createFormData,
  createFormFieldValueData
} from '@/utils/api/api';
import {createFromWithFieldObj} from '@/utils/schemaPageTemplate/createPageObjs';
interface ExcelImportPageProps {
  show: boolean;
  onClose: () => void;
  onConfirm: (pageId: any) => void;
  pageType?: any; // 添加页面类型属性
}

interface ExcelData {
  [key: string]: any;
}

interface ColumnMapping {
  excelColumn: string;
  fieldType: string;
  name: string;
  index?: number;
}

const ExcelImportPage: FC<ExcelImportPageProps> = ({
  show,
  onClose,
  onConfirm,
  pageType
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [excelData, setExcelData] = useState<ExcelData[]>([]);
  const [excelColumns, setExcelColumns] = useState<string[]>([]);
  const [fileName, setFileName] = useState('');
  const [columnMappings, setColumnMappings] = useState<ColumnMapping[]>([]);

  const [loading, setLoading] = useState(false);
  const createdPageIdRef = useRef<number | null>(null);

  // 重置状态
  const resetState = () => {
    setCurrentStep(0);
    setExcelData([]);
    setExcelColumns([]);
    setFileName('');
    setColumnMappings([]);
    setLoading(false);
    createdPageIdRef.current = null;
  };

  // 处理文件上传
  const handleFileUpload = (file: File) => {
    console.log('handleFileUpload file:', file);
    if (!file) return false;

    // 验证文件类型
    // const allowedTypes = [
    //   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    //   'application/vnd.ms-excel'
    // ];

    // if (!allowedTypes.includes(file.type)) {
    //   toast.error('只支持 .xlsx 和 .xls 格式的文件');
    //   return false;
    // }

    // // 验证文件大小 (2MB)
    // if (file.size > 2 * 1024 * 1024) {
    //   toast.error('文件大小不能超过 2MB');
    //   return false;
    // }

    setLoading(true);
    setFileName(file.name);

    const reader = new FileReader();
    console.log('reader:', reader);
    reader.onload = e => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, {type: 'array'});

        // 获取第一个工作表
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // 转换为JSON数据
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1});

        if (jsonData.length === 0) {
          toast.error('Excel文件为空或格式不正确');
          setLoading(false);
          return;
        }

        // 获取表头
        const headers = jsonData[0] as string[];
        const dataRows = jsonData.slice(1) as any[][];

        // 转换数据格式
        const formattedData = dataRows
          .map((row, index) => {
            const rowData: ExcelData = {_rowIndex: index + 2}; // Excel行号从2开始
            headers.forEach((header, colIndex) => {
              rowData[header] = row[colIndex] || '';
            });
            return rowData;
          })
          .filter(row => {
            // 过滤空行
            return Object.values(row).some(
              value => value !== '' && value !== null && value !== undefined
            );
          });

        setExcelColumns(headers);
        setExcelData(formattedData);

        // 初始化列映射 - 使用时间戳加随机数生成name
        const initialMappings: ColumnMapping[] = headers.map(
          (header, index) => ({
            excelColumn: header,
            name: `text_${Date.now()}_${Math.floor(Math.random() * 10000)}`,
            fieldType: 'input-text',
            index: index
          })
        );
        setColumnMappings(initialMappings);

        setCurrentStep(1);
        toast.success(`成功读取 ${formattedData.length} 行数据`);
      } catch (error) {
        console.error('Excel解析错误:', error);
        toast.error('Excel文件解析失败，请检查文件格式');
      } finally {
        setLoading(false);
      }
    };

    reader.readAsArrayBuffer(file);
    return false; // 阻止默认上传行为
  };

  // 更新列映射
  const updateColumnMapping = (
    index: number,
    field: keyof ColumnMapping,
    value: any
  ) => {
    const newMappings = [...columnMappings];
    newMappings[index] = {...newMappings[index], [field]: value};
    setColumnMappings(newMappings);
  };

  // 数据预览 - 第三步点击下一步时进行接口请求
  const handlePreview = async () => {
    const validMappings = columnMappings.filter(mapping => mapping.name);

    if (validMappings.length === 0) {
      toast.warning('请至少设置一个字段映射');
      return;
    }

    setLoading(true);

    try {
      // 1. 创建应用页面和分类
      // 从URL中获取应用ID
      const urlParams = new URLSearchParams(window.location.search);
      console.log('urlParams', urlParams);
      // 检查hash路由中的app ID
      const hashMatch = window.location.hash.match(/#\/app(\d+)/);
      const pathMatch = window.location.pathname.match(/\/app(\d+)/);
      const appIdFromUrl =
        urlParams.get('appId') || hashMatch?.[1] || pathMatch?.[1];
      console.log('Extracted appId:', appIdFromUrl);

      const pageData = {
        url: '',
        applicationId: Number(appIdFromUrl) || 0,
        applicantOrBackend: 1,
        pageType: pageType?.typeNumber || 1, // 使用选择的页面类型
        parentId: 0,
        name: `Excel导入-${fileName || '未命名'}`,
        type: 1
      };

      const pageResult = await createApplicationPageAndClass(pageData);

      if (pageResult.code !== 0) {
        toast.error('创建页面失败: ' + pageResult.msg);
        return;
      }

      const pageId = pageResult.data.id;
      createdPageIdRef.current = pageId;

      // 2. 生成表单字段数据
      const fieldData = validMappings.map(mapping => ({
        type: mapping.fieldType,
        label: mapping.excelColumn,
        name: mapping.name
      }));

      let data = createFromWithFieldObj(pageData.name, pageId, fieldData);

      // 3. 创建表单数据
      const formDataParams = {
        applicationPageId: pageId,
        data: JSON.stringify(data),
        field: JSON.stringify(fieldData)
      };

      const formResult = await createFormData(formDataParams);

      if (formResult.code !== 0) {
        toast.error('创建表单数据失败: ' + formResult.msg);
        return;
      }

      // 4. 上传Excel数据（预留接口）
      await uploadExcelData(pageId, validMappings);

      // 5. 直接跳转到完成步骤
      setCurrentStep(3);

      toast.success('页面创建成功，数据导入完成！');
    } catch (error) {
      console.error('创建页面和表单失败:', error);
      toast.error('创建页面和表单失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 上传Excel数据的预留接口
  const uploadExcelData = async (
    pageId: number,
    validMappings: ColumnMapping[]
  ) => {
    try {
      // 转换Excel数据为API需要的格式
      const uploadData = excelData.map(row => {
        const dataRow: any = {};
        validMappings.forEach(mapping => {
          dataRow[mapping.name] = row[mapping.excelColumn];
        });
        return dataRow;
      });
      console.log('uploadData', uploadData);
      // TODO: 这里预留数据上传接口
      const uploadResult = await createFormFieldValueData(pageId, {
        dynamicFields: uploadData
      });

      // if (!uploadResult.ok) {
      //   throw new Error('数据上传失败');
      // }

      console.log('Excel数据上传预留接口 - 数据:', {
        pageId,
        data: uploadData,
        totalRows: uploadData.length,
        fileName: fileName,
        mappings: validMappings
      });
    } catch (error) {
      console.error('Excel数据上传失败:', error);
      throw error;
    }
  };

  // 确认导入
  const handleConfirm = () => {
    console.log('createdPageId', createdPageIdRef.current);
    onConfirm(createdPageIdRef.current);
    handleClose();
  };

  // 关闭弹窗
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 生成amis schema
  const getSchema = () => {
    if (currentStep === 0) {
      // 第一步：文件上传
      return {
        type: 'dialog',
        title: 'Excel导入',
        size: 'lg',
        body: [
          {
            type: 'steps',
            value: currentStep,
            steps: [
              {title: '选择Excel', value: 0},
              {title: '数据预览', value: 1},
              {title: '表单设置', value: 2},
              {title: '导入数据', value: 3}
            ]
          },
          {
            type: 'divider'
          },
          loading
            ? {
                type: 'spinner',
                overlay: true,
                size: 'lg'
              }
            : {
                type: 'container',
                body: [
                  {
                    type: 'input-file',
                    name: 'excelFile',
                    label: '',
                    drag: true,
                    accept: '.xlsx,.xls',
                    asBlob: true,
                    receiver: {
                      url: 'javascript:void(0)',
                      method: 'post'
                    },
                    onEvent: {
                      change: {
                        actions: [
                          {
                            actionType: 'custom',
                            script: `
                          console.log("event:", event);
                          const file = event.data.file;
                          if (file) {
                          console.log("file:", file);
                          console.log("window.handleExcelUpload:", window.handleExcelUpload);
                            window.handleExcelUpload && window.handleExcelUpload(file.value);
                          }
                        `
                          },
                          {
                            actionType: 'toast',
                            args: {
                              msgType: 'info',
                              msg: '文件上传成功'
                            }
                          }
                        ]
                      }
                    },
                    description:
                      '点击或拖拽文件到此区域上传，支持 .xlsx 和 .xls 格式，文件大小不超过 2MB'
                  },
                  {
                    type: 'alert',
                    level: 'info',
                    body: {
                      type: 'tpl',
                      tpl: `
                    <h4>上传Excel表格的规则：</h4>
                    <ol>
                      <li>不能存在合并的单元格</li>
                      <li>文件大小不超过2MB</li>
                      <li>仅支持 (*.xls和*.xlsx) 文件</li>
                      <li>请确保导入的sheet表头中不包含空的单元格，否则该sheet的数据系统将不做导入</li>
                    </ol>
                  `
                    }
                  }
                ]
              }
        ],
        actions: [
          {
            type: 'button',
            label: '取消',
            actionType: 'cancel'
          }
        ]
      };
    } else if (currentStep === 1) {
      // 第二步：数据预览
      const previewColumns = excelColumns.map(column => ({
        name: column,
        label: column,
        type: 'tpl',
        headerAlign: 'center',
        tpl: `
          <div style="text-align: center; padding: 8px;">
            <div style="color: #333; font-size: 14px;">\${${column} || '文本'}</div>
          </div>
        `
      }));

      return {
        type: 'dialog',
        title: pageType ? `${pageType.name} - Excel导入` : '新建表单',
        size: 'xl',
        body: [
          {
            type: 'steps',
            value: currentStep,
            steps: [
              {title: '选择Excel', value: 0},
              {title: '数据预览', value: 1},
              {title: '表单设置', value: 2},
              {title: '导入数据', value: 3}
            ]
          },
          {
            type: 'divider'
          },
          {
            type: 'container',
            body: [
              {
                type: 'flex',
                justify: 'space-between',
                alignItems: 'center',
                style: {
                  marginBottom: '16px'
                },
                items: [
                  {
                    type: 'tpl',
                    tpl: '工作表',
                    style: {
                      fontSize: '14px',
                      fontWeight: 'bold'
                    }
                  },
                  {
                    type: 'select',
                    name: 'worksheet',
                    value: 'sheet1',
                    options: [{label: 'sheet1', value: 'sheet1'}],
                    style: {
                      width: '120px'
                    }
                  }
                ]
              },
              {
                type: 'table',
                data: {
                  items: excelData.slice(0, 5) // 只显示前5行数据
                },
                columns: previewColumns,
                headerToolbar: [],
                footerToolbar: [],
                style: {
                  border: '1px solid #e6e6e6'
                },
                className: 'excel-preview-table'
              }
            ]
          }
        ],
        actions: [
          {
            type: 'button',
            label: '取消',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'custom',
                    script:
                      'window.setExcelImportStep && window.setExcelImportStep(0)'
                  }
                ]
              }
            }
          },
          {
            type: 'button',
            label: '下一步',
            level: 'primary',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'custom',
                    script:
                      'window.setExcelImportStep && window.setExcelImportStep(2)'
                  }
                ]
              }
            }
          }
        ]
      };
    } else if (currentStep === 2) {
      // 第三步：表单设置
      // 生成输入类型选项
      const inputTypeOptions = formItems[0].children.map((item: any) => ({
        label: item.label,
        value: item.value
      }));

      return {
        type: 'dialog',
        title: pageType ? `${pageType.name} - 表单设置` : '新建表单',
        size: 'lg',
        body: [
          {
            type: 'steps',
            value: currentStep,
            steps: [
              {title: '选择Excel', value: 0},
              {title: '数据预览', value: 1},
              {title: '表单设置', value: 2},
              {title: '导入数据', value: 3}
            ]
          },
          {
            type: 'divider'
          },
          {
            type: 'form',
            api: 'javascript:void(0)',
            body: [
              {
                type: 'table',
                data: {
                  items: columnMappings
                },
                columns: [
                  {
                    name: 'index',
                    label: '序号',
                    type: 'tpl',
                    tpl: '${index | plus: 1}',
                    width: 80
                  },
                  {
                    name: 'excelColumn',
                    label: 'Excel列名',
                    type: 'tpl',
                    tpl: '${excelColumn}',
                    width: 200
                  },
                  {
                    name: 'name',
                    label: '字段名称',
                    type: 'tpl',
                    tpl: '${name}',
                    width: 200
                  },
                  {
                    name: 'fieldType',
                    label: '输入类型',
                    type: 'select',
                    options: inputTypeOptions,
                    width: 200,
                    onEvent: {
                      change: {
                        actions: [
                          {
                            actionType: 'custom',
                            script: `
                              const rowIndex = event.data.__rowIndex;
                              const value = event.data.value;
                              window.updateColumnMapping && window.updateColumnMapping(rowIndex, 'fieldType', value);
                            `
                          }
                        ]
                      }
                    }
                  }
                ],
                headerToolbar: [],
                footerToolbar: []
              }
            ]
          }
        ],
        actions: [
          {
            type: 'button',
            label: '上一步',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'custom',
                    script:
                      'window.setExcelImportStep && window.setExcelImportStep(1)'
                  }
                ]
              }
            }
          },
          {
            type: 'button',
            label: '下一步',
            level: 'primary',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'custom',
                    script:
                      'window.handleExcelPreview && window.handleExcelPreview()'
                  }
                ]
              }
            }
          }
        ]
      };
    } else if (currentStep === 3) {
      // 第四步：导入数据完成
      return {
        type: 'dialog',
        title: '新建表单',
        size: 'lg',
        body: [
          {
            type: 'steps',
            value: currentStep,
            steps: [
              {title: '选择Excel', value: 0},
              {title: '数据预览', value: 1},
              {title: '表单设置', value: 2},
              {title: '导入数据', value: 3}
            ]
          },
          {
            type: 'divider'
          },
          {
            type: 'container',
            style: {
              textAlign: 'center',
              padding: '40px 0'
            },
            body: [
              {
                type: 'tpl',
                tpl: '<div style="margin-bottom: 20px;"><svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#4CAF50" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M8 12l2 2 6-6"></path></svg></div>',
                inline: false
              },
              {
                type: 'tpl',
                tpl: '<div style="font-size: 20px; font-weight: bold; margin-bottom: 10px;">数据导入完成</div>',
                inline: false
              },
              {
                type: 'tpl',
                tpl: `<div style="color: #666; margin-bottom: 5px;">共导入${excelData.length}条数据，导入失败0条数据</div>`,
                inline: false
              }
            ]
          }
        ],
        actions: [
          {
            type: 'button',
            label: '完成',
            level: 'primary',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'custom',
                    script:
                      'window.handleExcelConfirm && window.handleExcelConfirm()'
                  }
                ]
              }
            }
          }
        ]
      };
    }

    return {};
  };

  // 将处理函数挂载到window对象
  React.useEffect(() => {
    (window as any).handleExcelUpload = handleFileUpload;
    (window as any).setExcelImportStep = setCurrentStep;
    (window as any).handleExcelPreview = handlePreview;
    (window as any).handleExcelConfirm = handleConfirm;
    (window as any).updateColumnMapping = updateColumnMapping;

    return () => {
      delete (window as any).handleExcelUpload;
      delete (window as any).setExcelImportStep;
      delete (window as any).handleExcelPreview;
      delete (window as any).handleExcelConfirm;
      delete (window as any).updateColumnMapping;
    };
  }, [columnMappings, excelData]);

  return (
    <AMISRenderer schema={getSchema()} show={show} onClose={handleClose} />
  );
};

export default ExcelImportPage;
