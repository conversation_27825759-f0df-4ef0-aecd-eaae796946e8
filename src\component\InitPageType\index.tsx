import React, {FC} from 'react';
import './index.scss';
import {toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import ExcelImportPage from '@/component/ExcelImportPage';

// 创建云网盘
import yunWangPan from './image/yunWangPan.png';
// 导入页面类型图片
import type_form from '@/image/page_icons/type_form.png';
import type_process from '@/image/page_icons/type_process.png';
// 创建添加外部链接
import {linkPopup} from './common/linkPopup';
// 创建报表
import {reportPopup} from './common/reportPopup';
// 创建wiki页面
import wikiPopup from './common/createWikiPagePopup';
// 创建自定义页面
import customPagePopup from './common/createCustomPagePopup';
// 创建增删改查页面
import crudPagePopup from './common/createCRUDPagePopup';
// 创建amis报表页面
import dashboardPopup from './common/createDashboardPopup';
// 创建云网盘
import yunWangPanPopup from './common/createYunWangPanPopup';
// 创建笔记
import notesPopup from './common/createNotesPopup';
// 创建白板
import whiteboardPopup from './common/createWhiteboardPopup';
// 创建多维表格
import multiDimensionalTablePopup from './common/createMultiDimensionalTablePopup';
// 创建数据源导入
import dataSetImportPopup from './common/createDataSetImportPagePopup';
// 创建芋道网盘
import yudaoPanPopup from './common/createYudaoPanPopup';
// 创建门户
import portletPopup from './common/createPortletPopup';
// 创建Excel导入
import excelImportPopup from './common/createExcelImportPopup';
// 创建简单名称输入对话框
import createSimpleNameDialog from './common/createSimpleNameDialog';
// schema
import {
  createFromObj,
  createCustompageObj,
  createIframeObj,
  createReportObj,
  createDatasetImportObj,
  createPanObj
} from '@/utils/schemaPageTemplate/createPageObjs';

import {
  // 创建页面接口
  createRute,
  // 保存页面schema的接口
  ruteCodeSave,
  // 创建报表接口
  createDashboardReport,
  // 保存wiki,报表code的接口
  savePageTypeCode,
  // 创建wiki接口
  createWiki,
  getFormFieldPage,
  createApplicationPageAndClass, // 创建应用页面和分类
  createFormData, // 创建表单数据
  getDataSetPage, // 获取数据源导入页面
  updateFormData, // 更新表单数据
  getFormDataPage,
  updateApplicationPageAndClass
} from '@/utils/api/api';
import { width } from '@/editor/EChartsEditor/Common';

const InitPageType: FC<any> = (props: any) => {
  /* data 数据 */
  // 页面类型列表
  const [pageTypeList, setPageTypeList] = React.useState<any>([]);
  const [appId, setAppId] = React.useState<any>(0);
  // 是否在分类下创建-临时选中的页面在某个分类下
  const [checkPageItem, setCheckPageItem] = React.useState<any>({});
  // 当前选择的要创建的页面分类
  const [currentPageType, setCurrentPageType] = React.useState<any>({});

  // 打开新建外部链接弹窗
  const [openLinkPopup, setOpenLinkPopup] = React.useState<any>(false);

  // 打开新建报表弹窗
  const [openReportPopup, setOpenReportPopup] = React.useState<any>(false);

  // 打开新建wiki弹窗
  const [openWikiPopup, setOpenWikiPopup] = React.useState<any>(false);

  // 打开新建自定义页面弹窗
  const [openCustomPagePopup, setOpenCustomPagePopup] =
    React.useState<any>(false);

  // 打开新建amis报表弹窗
  const [openDashboardPopup, setOpenDashboardPopup] =
    React.useState<any>(false);

  // 打开新建云网盘弹窗
  const [openYunWangPanPopup, setOpenYunWangPanPopup] =
    React.useState<any>(false);

  // 打开新建笔记弹窗
  const [openNotesPopup, setOpenNotesPopup] = React.useState<any>(false);

  // 打开新建白板弹窗
  const [openWhiteboardPopup, setOpenWhiteboardPopup] =
    React.useState<any>(false);

  // 打开新建多维表格弹窗
  const [openMultiDimensionalTablePopup, setOpenMultiDimensionalTablePopup] =
    React.useState<any>(false);

  // 打开新建增删改查弹窗
  const [openCRUDPopup, setOpenCRUDPopup] = React.useState<any>(false);

  // 打开新建数据源导入弹窗
  const [openDataSetImportPopup, setOpenDataSetImportPopup] =
    React.useState<any>(false);

  // 数据源导入页面列表
  const [dataSetPageList, setDataSetPageList] = React.useState<any>([]);

  // 打开新建芋道网盘弹窗
  const [openYudaoPanPopup, setOpenYudaoPanPopup] = React.useState<any>(false);
  /* data 数据 end */

  // 打开新建门户弹窗
  const [openPortletPopup, setOpenPortletPopup] = React.useState<any>(false);

  // 打开新建Excel导入弹窗
  const [openExcelImportPopup, setOpenExcelImportPopup] =
    React.useState<any>(false);

  // 打开Excel导入类型选择弹窗
  const [openExcelImportTypePopup, setOpenExcelImportTypePopup] =
    React.useState<any>(false);

  // 选中的Excel导入页面类型
  const [selectedExcelImportType, setSelectedExcelImportType] =
    React.useState<any>(null);

  // 新建分类对话框
  const [showCategoryDialog, setShowCategoryDialog] = React.useState(false);
  const [categoryInfo, setCategoryInfo] = React.useState<any>(null);

  // 新建表单对话框
  const [showFormDialog, setShowFormDialog] = React.useState(false);
  const [formPageType, setFormPageType] = React.useState<any>(null);

  // 监听页面类型弹窗触发事件
  React.useEffect(() => {
    const handleTriggerPageTypeDialog = (event: any) => {
      const { dialogType, pageType } = event.detail;
      console.log('InitPageType 接收到弹窗触发事件:', dialogType, pageType);

      // 设置当前页面类型
      setCurrentPageType(pageType);

      // 根据对话框类型触发相应的弹窗
      switch (dialogType) {
        case 'showLinkDialog':
          setOpenLinkPopup(true);
          break;
        case 'showCustomPageDialog':
          setOpenCustomPagePopup(true);
          break;
        case 'showDashboardDialog':
          setOpenDashboardPopup(true);
          break;
        case 'showPortletDialog':
          setOpenPortletPopup(true);
          break;
        case 'showDataSetImportDialog':
          setOpenDataSetImportPopup(true);
          break;
        case 'showYudaoPanDialog':
          setOpenYudaoPanPopup(true);
          break;
        default:
          console.log('未知弹窗类型:', dialogType);
      }
    };

    // 添加事件监听器
    window.addEventListener('triggerPageTypeDialog', handleTriggerPageTypeDialog);

    // 清理函数
    return () => {
      window.removeEventListener('triggerPageTypeDialog', handleTriggerPageTypeDialog);
    };
  }, []);

  /* methods 方法 */
  const handleCreatePageClick = (item: any) => {
    setCurrentPageType(item);
    switch (item.typeName) {
      case 'form':
        handleCreatePage(item);
        break;
      case 'process':
        handleCreatePage(item);
        break;
      case 'custompage':
        setOpenCustomPagePopup(true);
        break;
      case 'dashboard':
        setOpenDashboardPopup(true);
        break;
      case 'link':
        setOpenLinkPopup(true);
        break;
      case 'report':
        setOpenReportPopup(true);
        break;
      case 'wiki':
        setOpenWikiPopup(true);
        break;
      case 'yunWangPan':
        setOpenYunWangPanPopup(true);
        break;
      case 'notes':
        setOpenNotesPopup(true);
        break;
      case 'whiteboard':
        setOpenWhiteboardPopup(true);
        break;
      case 'multiDimensionalTable':
        setOpenMultiDimensionalTablePopup(true);
        break;
      case 'CRUD':
        setOpenCRUDPopup(true);
        break;
      case 'dataSetImport':
        setOpenDataSetImportPopup(true);
        break;
      case 'yudaopan':
        setOpenYudaoPanPopup(true);
        break;
      case 'portlet':
        setOpenPortletPopup(true);
        break;
      case 'excelImport':
        setOpenExcelImportTypePopup(true);
        break;
    }
  };
  // 计算该应用总的有多少页面
  const getTotalPages = () => {
    let res = 1;
    if (props.appMenuList) {
      for (let i = 0; props.appMenuList.length > i; i++) {
        if (props.appMenuList[i].children) {
          res += props.appMenuList[i].children.length;
        } else {
          res++;
        }
      }
    }
    return res;
  };

  // 创建页面
  const handleCreatePage = (item: any, val?: any) => {
    console.log(val);

    // 获取图标信息 - 优先使用currentPageType中的图标信息
    const iconClass = currentPageType?.icon || item.icon || '';
    const iconColor = currentPageType?.iconColor || item.iconColor || '';

    // 确定parentId：如果选中的是分类，则将新页面归属到该分类下
    let parentId = 0;
    if (checkPageItem && checkPageItem.type === 2) {
      // 当前选中的是分类
      parentId = checkPageItem.id;
      console.log(`🔄 InitPageType - 将页面归属到选中的分类 ${checkPageItem.name}(${parentId}) 下`);
    } else if (checkPageItem && checkPageItem.parentId) {
      // 当前选中的是页面，将新页面归属到同一个分类下
      parentId = checkPageItem.parentId;
      console.log(`🔄 InitPageType - 将页面归属到与选中页面相同的分类 ${parentId} 下`);
    } else {
      console.log('🔄 InitPageType - 将页面归属到根级别');
    }

    let data: any = {
      url: val?.link ? val.link : '',
      applicationId: appId,
      applicantOrBackend: 1,
      pageType: item.typeNumber,
      parentId: parentId,
      name: val?.name ? val.name : `页面 ${getTotalPages()}`,
      type: 1,
      // 添加图标信息 - 构建带颜色的HTML格式
      icon: iconClass && iconColor ?
        `<span style="color:${iconColor};display:inline-flex;align-items:center;justify-content:center;"><i class="${iconClass}"></i></span>` :
        iconClass,
      iconColor: iconColor
    };
    if (item.typeName === 'dataSetImport') {
      data.dataSetId = val?.dataSetId ? val.dataSetId : '';
    }


    createApplicationPageAndClass(data).then((res: any) => {
      if (res.code == 0) {
        // 普通表单
        handleCreatePageSuccess(item, data, res);
      } else {
        toast.error('创建失败 ', res.msg);
      }
    });
  };
  // 创建完成后处理数据
  const handleCreatePageSuccess = (item: any, data: any, res: any) => {
    let pageId = res.data.id;
    switch (item.typeName) {
      case 'form':
        initFormData(pageId, data);
        break;
      case 'process':
        initFormData(pageId, data);
        break;
      case 'report':
        setOpenReportPopup(false);
        props.onSaveSuccess(pageId);
        break;
      case 'link':
        setOpenLinkPopup(false);
        props.onSaveSuccess(pageId);
        break;
      case 'custompage':
        initCustompageData(pageId, data);
        break;
      case 'wiki':
        setOpenWikiPopup(false);
        props.onSaveSuccess(pageId);
        break;
      case 'dashboard':
        initCustompageData(pageId, data);
        props.onSaveSuccess(pageId);
        setOpenDashboardPopup(false);
        break;
      case 'yunWangPan':
        setOpenYunWangPanPopup(false);
        props.onSaveSuccess(pageId);
        break;
      case 'notes':
        setOpenNotesPopup(false);
        props.onSaveSuccess(pageId);
        break;
      case 'whiteboard':
        setOpenWhiteboardPopup(false);
        props.onSaveSuccess(pageId);
        break;
      case 'multiDimensionalTable':
        setOpenMultiDimensionalTablePopup(false);
        props.onSaveSuccess(pageId);
        break;
      case 'CRUD':
        setOpenCRUDPopup(false);
        props.onSaveSuccess(pageId);
        break;
      case 'dataSetImport':
        initDataSetImportData(pageId, data);
        props.onSaveSuccess(pageId);
        break;
      case 'yudaopan':
        // setOpenYudaoPanPopup(false);
        // props.onSaveSuccess(pageId);
        initPanpageData(pageId, data);
        break;
      case 'portlet':
        initCustompageData(pageId, data);
        props.onSaveSuccess(pageId);
        setOpenPortletPopup(false);
        break;
      case 'excelImport':
        initExcelImportData(pageId, data);
        break;
    }
  };
  // 初始化表单数据-创建表单数据
  const initFormData = (pageId: any, info: any) => {
    let data = {
      applicationPageId: pageId,
      data: window.JSON.stringify(createFromObj(info.name, pageId)),
      field: ''
    };
    createFormData(data).then((res: any) => {
      if (res.code == 0) {
        toast.success('初始化数据完成~');
        props.history.push(
          `/app${props.computedMatch.params.appId}/design/page${pageId}?editCode=${pageId}`
        );
        // props.onSaveSuccess(pageId);
      } else {
        toast.error('初始化数据失败 ' + res.msg);
      }
    });
  };
  // 初始化表单数据-创建自定义页面
  const initCustompageData = (pageId: any, info: any) => {
    let data = {
      applicationPageId: pageId,
      data: window.JSON.stringify(createCustompageObj(info.name)),
      field: ''
    };
    createFormData(data).then((res: any) => {
      if (res.code == 0) {
        toast.success('初始化数据完成~');
        setOpenCustomPagePopup(false);
        props.history.push(
          `/app${props.computedMatch.params.appId}/design/page${pageId}?editCode=${pageId}`
        );
        props.onSaveSuccess(pageId);
      } else {
        toast.error('初始化数据失败 ' + res.msg);
      }
    });
  };

  const initPanpageData = (pageId: any, info: any) => {
    let data = {
      applicationPageId: pageId,
      data: window.JSON.stringify(createPanObj(info.name, pageId)),
      field: ''
    };
    createFormData(data).then((res: any) => {
      if (res.code == 0) {
        toast.success('初始化数据完成~');
        setOpenYudaoPanPopup(false);
        props.onSaveSuccess(pageId);
      } else {
        toast.error('初始化数据失败 ' + res.msg);
      }
    });
  };

  // 初始化Excel导入数据
  const initExcelImportData = (pageId: any, info: any) => {
    // 创建Excel导入页面的schema
    const excelImportSchema = {
      type: 'page',
      title: info.name || 'Excel导入页面',
      body: [
        {
          type: 'alert',
          level: 'info',
          body: `已成功导入 ${info.totalRows || 0} 行数据`
        },
        {
          type: 'crud',
          api: {
            method: 'get',
            url: `/admin-api/system/form-field-value/page?applicationPageId=${pageId}`
          },
          columns: info.columnMappings?.map((mapping: any) => ({
            name: mapping.targetField,
            label: mapping.targetField,
            type: mapping.fieldType === 'number' ? 'number' : 'text'
          })) || []
        }
      ]
    };

    let data = {
      applicationPageId: pageId,
      data: window.JSON.stringify(excelImportSchema),
      field: window.JSON.stringify(info.columnMappings || [])
    };

    createFormData(data).then((res: any) => {
      if (res.code == 0) {
        // 如果有Excel数据，批量插入
        if (info.excelData && info.excelData.length > 0) {
          // 这里可以调用批量插入API
          console.log('Excel数据待插入:', info.excelData);
        }

        toast.success('Excel导入页面创建完成~');
        setOpenExcelImportPopup(false);
        props.onSaveSuccess(pageId);
      } else {
        toast.error('初始化数据失败 ' + res.msg);
      }
    });
  };

  // 初始化表单数据-创建数据源导入页面
  const initDataSetImportData = (pageId: any, info: any) => {
    handleGetFormDataPage(pageId, info);
  };

  // 获取表单数据 pageData
  const handleGetFormDataPage = (pageId: any, info: any) => {
    let data = {
      applicationPageId: pageId,
      pageNo: 1,
      pageSize: 10
    };
    getFormDataPage(data).then((res: any) => {
      if (res.code == 0) {
        if (res.data.list.length > 0) {
          if (res.data.list[0]) {
            updateFromSchema(pageId, info, res.data.list[0]);
          } else {
          }
        } else {
        }
      } else {
        toast.error(res.msg);
      }
    });
  };

  const updateFromSchema = (pageId: any, info: any, pageData: any) => {
    let field = JSON.parse(pageData.field);
    let schema = window.JSON.stringify(
      createDatasetImportObj(info.name, pageId, field, info.dataSetId)
    );

    let data = {
      id: pageData.id,
      data: schema,
      field: pageData.field,
      applicationPageId: pageId
    };
    updateFormData(data).then((res: any) => {
      if (res.code == 0) {
        setOpenDataSetImportPopup(false);
        props.onSaveSuccess(pageId);
      } else {
        toast.error(res.msg);
      }
    });
  };

  // 创建外部链接
  const createLinkPopup = (val: any) => {
    handleCreatePage(currentPageType, val[0]);
  };
  // 创建自定义页面
  const createCustomPagePopup = (val: any) => {
    handleCreatePage(currentPageType, val[0]);
  };
  // 创建amis报表
  const createDashboardPopup = (val: any) => {
    handleCreatePage(currentPageType, val[0]);
  };

  // 创建报表页面
  const createReportPopup = (val: any) => {
    handleCreatePage(currentPageType, val[0]);
  };
  // 创建wiki页面
  const createWikiPopup = (val: any) => {
    handleCreatePage(currentPageType, val[0]);
  };

  // 创建芋道网盘
  const createYudaoPanPopup = (val: any) => {
    handleCreatePage(currentPageType, val[0]);
  };
  // 选择Excel导入类型
  const handleExcelImportTypeSelect = (type: any) => {
    setSelectedExcelImportType(type);
    setOpenExcelImportTypePopup(false);
    setOpenExcelImportPopup(true);
  };

  // 创建Excel导入
  const createExcelImportPopup = (val: any) => {
    console.log('Excel导入', val);
    //跳转页面
    props.history.push(
      `/app${props.computedMatch.params.appId}/design/page${val}?editCode=${val}`
    );
  };
  // 创建网盘
  const createYunWangPanPopup = (val: any) => {
    // 创建一个新对象，包含原对象的所有属性和新增的link属性
    const newVal = {
      ...val[0],
      link: 'https://jmalcloud.ykddm.fjpipixia.com/login?username=ykadm&password=Yk%40123456'
    };
    handleCreatePage(currentPageType, newVal);
  };

  // 创建笔记
  const createNotesPopup = (val: any) => {
    // 创建一个新对象，包含原对象的所有属性和新增的link属性
    const newVal = {
      ...val[0],
      link: 'https://affine.ykddm.fjpipixia.com/?username=<EMAIL>&password=Yk%40123456&mode=page'
    };
    handleCreatePage(currentPageType, newVal);
  };
  // 创建白板
  const createWhiteboardPopup = (val: any) => {
    // 创建一个新对象，包含原对象的所有属性和新增的link属性
    const newVal = {
      ...val[0],
      link: 'https://affine.ykddm.fjpipixia.com/?username=<EMAIL>&password=Yk%40123456&mode=edgeless'
    };
    handleCreatePage(currentPageType, newVal);
  };
  // 创建多维表格
  const createMultiDimensionalTablePopup = (val: any) => {
    handleCreatePage(currentPageType, val[0]);
  };
  // 创建增删改查页面
  const createCRUDPopup = (val: any) => {
    handleCreatePage(currentPageType, val[0]);
  };
  // 创建数据源导入
  const createDataSetImportPopup = (val: any) => {
    console.log(val);
    handleCreatePage(currentPageType, val[0]);
  };

  // 创建门户
  const createPortletPopup = (val: any) => {
    handleCreatePage(currentPageType, val[0]);
  };

  /* methods 方法 end */

  // 处理直接创建的逻辑
  const handleDirectCreate = (checkedData: any) => {
    console.log('🔄 InitPageType - 处理直接创建:', checkedData);

    if (checkedData.createType === 'category') {
      // 新建分类 - 显示分类名称输入对话框
      setCategoryInfo(checkedData.categoryInfo);
      setShowCategoryDialog(true);
    } else if (checkedData.createType === 'page' && checkedData.pageType) {
      // 新建页面 - 根据页面类型显示相应的对话框
      const pageType = checkedData.pageType;
      console.log('直接创建页面:', pageType);

      switch (pageType.typeName) {
        case 'form':
        case 'process':
          // 新建普通表单或流程表单 - 显示表单名称输入对话框
          setFormPageType(pageType);
          setShowFormDialog(true);
          break;
        case 'excelImport':
          // 新建Excel导入 - 显示类型选择对话框
          setOpenExcelImportTypePopup(true);
          break;
        default:
          // 其他类型按原有逻辑处理
          handlePageTypeClick(pageType.typeName);
          break;
      }
    }
  };

  /* created 初始化 */
  React.useEffect(() => {
    setPageTypeList(props.pageTypeList);
    setAppId(
      props.computedMatch.params.appId ? props.computedMatch.params.appId : ''
    );

    // 设置选中的页面信息，用于确定新页面的parentId
    if (props.checkedPageData) {
      setCheckPageItem(props.checkedPageData);
      console.log('🔄 InitPageType - 接收到选中的页面/分类信息:', props.checkedPageData);

      // 检查是否是直接创建
      if (props.checkedPageData.createType) {
        handleDirectCreate(props.checkedPageData);
      }
    }
  }, [props]);

  /* created 初始化 end */

  return (
    <div className="initPageTypeContent">
      <div className="initPageTypeContent-title">
        选择页面类型，开始构建页面
      </div>
      <div className="initPageTypeContent-tips">
        表单、报表、展示页、从哪开始？点击
        <span className="initPageTypeContent-tips-link">了解更多</span>
      </div>
      <div className="initPageTypeContent-menuList">
        {pageTypeList.map((item: any, index: number) => {
          return (
            <div
              key={index}
              onClick={() => handleCreatePageClick(item)}
              className="initPageTypeContent-menuList-item"
            >
              <div className="initPageTypeContent-menuList-item-icon">
                <img
                  className="initPageTypeContent-menuList-item-icon-img"
                  src={item.imgSrc}
                />
              </div>
              <div className="initPageTypeContent-menuList-item-name">
                {item.name}
              </div>
              <div className="initPageTypeContent-menuList-item-tips">
                {item.tips}
              </div>
            </div>
          );
        })}
      </div>

      {/* 报表页面 3 */}
      {openReportPopup && (
        <AMISRenderer
          show={openReportPopup}
          onClose={() => setOpenReportPopup(false)}
          onConfirm={(val: any) => createReportPopup(val)}
          schema={reportPopup()}
        />
      )}
      {/* 外部链接 4 */}
      {openLinkPopup && (
        <AMISRenderer
          show={openLinkPopup}
          onClose={() => {
            setOpenLinkPopup(false);
            setCurrentPageType({});
          }}
          onConfirm={(val: any) => createLinkPopup(val)}
          schema={linkPopup()}
        />
      )}
      {/* 自定义页面 5*/}
      {openCustomPagePopup && (
        <AMISRenderer
          show={openCustomPagePopup}
          onClose={() => setOpenCustomPagePopup(false)}
          onConfirm={(val: any) => createCustomPagePopup(val)}
          schema={customPagePopup()}
        />
      )}
      {/* wiki文档 6 */}
      {openWikiPopup && (
        <AMISRenderer
          show={openWikiPopup}
          onClose={() => setOpenWikiPopup(false)}
          onConfirm={(val: any) => createWikiPopup(val)}
          schema={wikiPopup()}
        />
      )}
      {/* amis报表 7 */}
      {openDashboardPopup && (
        <AMISRenderer
          show={openDashboardPopup}
          onClose={() => setOpenDashboardPopup(false)}
          onConfirm={(val: any) => createDashboardPopup(val)}
          schema={dashboardPopup()}
        />
      )}
      {/* 云盘 8 */}
      {openYunWangPanPopup && (
        <AMISRenderer
          show={openYunWangPanPopup}
          onClose={() => setOpenYunWangPanPopup(false)}
          onConfirm={(val: any) => createYunWangPanPopup(val)}
          schema={yunWangPanPopup()}
        />
      )}
      {/* 笔记 9*/}
      {openNotesPopup && (
        <AMISRenderer
          show={openNotesPopup}
          onClose={() => setOpenNotesPopup(false)}
          onConfirm={(val: any) => createNotesPopup(val)}
          schema={notesPopup()}
        />
      )}
      {/* 白板 10 */}
      {openWhiteboardPopup && (
        <AMISRenderer
          show={openWhiteboardPopup}
          onClose={() => setOpenWhiteboardPopup(false)}
          onConfirm={(val: any) => createWhiteboardPopup(val)}
          schema={whiteboardPopup()}
        />
      )}
      {/* 多维表格 11 */}
      {openMultiDimensionalTablePopup && (
        <AMISRenderer
          show={openMultiDimensionalTablePopup}
          onClose={() => setOpenMultiDimensionalTablePopup(false)}
          onConfirm={(val: any) => createMultiDimensionalTablePopup(val)}
          schema={multiDimensionalTablePopup()}
        />
      )}
      {/* 增删改查 12 */}
      {/* {openCRUDPopup && (
        <AMISRenderer
          show={openCRUDPopup}
          onClose={() => setOpenCRUDPopup(false)}
          onConfirm={(val: any) => createCRUDPopup(val)}
          schema={crudPagePopup()}
        />
      )} */}
      {/* 数据源导入 13 */}
      {openDataSetImportPopup && (
        <AMISRenderer
          show={openDataSetImportPopup}
          onClose={() => setOpenDataSetImportPopup(false)}
          onConfirm={(val: any) => createDataSetImportPopup(val)}
          schema={dataSetImportPopup(appId)}
        />
      )}

      {/* 芋道网盘 14 */}
      {openYudaoPanPopup && (
        <AMISRenderer
          show={openYudaoPanPopup}
          onClose={() => setOpenYudaoPanPopup(false)}
          onConfirm={(val: any) => createYudaoPanPopup(val)}
          schema={yudaoPanPopup()}
        />
      )}

      {/* 门户 17 */}
      {openPortletPopup && (
        <AMISRenderer
          show={openPortletPopup}
          onClose={() => setOpenPortletPopup(false)}
          onConfirm={(val: any) => createPortletPopup(val)}
          schema={portletPopup()}
        />
      )}

      {/* Excel导入类型选择 */}
      {openExcelImportTypePopup && (
        <AMISRenderer
          show={openExcelImportTypePopup}
          onClose={() => setOpenExcelImportTypePopup(false)}
          onConfirm={(val: any) => {
            console.log("Excel导入类型选择", val);
            const selectedType = val[0]?.selectedType;
            if (selectedType) {
              handleExcelImportTypeSelect(selectedType);
            }
          }}
          schema={{
            type: 'dialog',
            title: '选择EXCEL导入的页面类型',
            size: 'md',
            body: [
              {
                type: 'form',
                body: [
                  {
                    type: 'container',
                    body: [
                      {
                        type: 'tpl',
                        tpl: '<div style="margin-bottom: 16px; font-size: 16px; font-weight: bold;">请选择要创建的页面类型：</div>'
                      },
                      {
                        type: 'flex',
                        // direction: 'column',
                        gap: 'md',
                        items: [
                          {
                            type: 'container',
                            className: 'excel-type-option',
                            style: {
                              border: '2px solid #e6e6e6',
                              borderRadius: '8px',
                              padding: '16px',
                              cursor: 'pointer',
                              transition: 'all 0.3s ease'
                            },
                            onEvent: {
                              click: {
                                actions: [
                                  {
                                    actionType: 'setValue',
                                    componentId: 'selectedTypeField',
                                    args: {
                                      value: {
                                        name: '新建普通表单',
                                        typeNumber: 1,
                                        typeName: 'form',
                                        imgSrc: type_form
                                      }
                                    }
                                  }
                                ]
                              }
                            },
                            body: [
                              {
                                type: 'flex',
                                direction: 'column',
                                alignItems: 'center',
                                items: [
                                  {
                                    type: 'image',
                                    src: type_form,
                                    imageMode: 'original',
                                    width: 40,
                                    height: 40,
                                    style: { marginBottom: '16px' }
                                  },
                                  {
                                    type: 'container',
                                    body: [
                                      {
                                        type: 'tpl',
                                        tpl: '<div style="font-size: 16px; font-weight: bold; margin-bottom: 4px;">新建普通表单</div>'
                                      },
                                      {
                                        type: 'tpl',
                                        tpl: '<div style="font-size: 14px; color: #666;">数据收集、事件跟进</div>'
                                      }
                                    ]
                                  }
                                ]
                              }
                            ]
                          },
                          {
                            type: 'container',
                            className: 'excel-type-option',
                            style: {
                              border: '2px solid #e6e6e6',
                              borderRadius: '8px',
                              padding: '16px',
                              cursor: 'pointer',
                              transition: 'all 0.3s ease',
                              marginLeft: '16px'
                            },
                            onEvent: {
                              click: {
                                actions: [
                                  {
                                    actionType: 'setValue',
                                    componentId: 'selectedTypeField',
                                    args: {
                                      value: {
                                        name: '新建流程表单',
                                        typeNumber: 2,
                                        typeName: 'process',
                                        imgSrc: type_process
                                      }
                                    }
                                  }
                                ]
                              }
                            },
                            body: [
                              {
                                type: 'flex',
                                direction: 'column',
                                alignItems: 'center',
                                items: [
                                  {
                                    type: 'image',
                                    src: type_process,
                                    width: 40,
                                    height: 40,
                                    imageMode: 'original',

                                    style: { marginBottom: '16px' }
                                  },
                                  {
                                    type: 'container',
                                    body: [
                                      {
                                        type: 'tpl',
                                        tpl: '<div style="font-size: 16px; font-weight: bold; margin-bottom: 4px;">新建流程表单</div>'
                                      },
                                      {
                                        type: 'tpl',
                                        tpl: '<div style="font-size: 14px; color: #666;">业务审批、任务协同</div>'
                                      }
                                    ]
                                  }
                                ]
                              }
                            ]
                          }
                        ]
                      },
                      {
                        type: 'hidden',
                        name: 'selectedType',
                        id: 'selectedTypeField'
                      }
                    ]
                  }
                ]
              }
            ],
            actions: [
              {
                type: 'button',
                label: '取消',
                actionType: 'cancel'
              },
              {
                type: 'button',
                label: '确认',
                level: 'primary',
                actionType: 'submit'
              }
            ]
          }}
        />
      )}

      {/* Excel导入 18 */}
      {openExcelImportPopup && (
        <ExcelImportPage
          show={openExcelImportPopup}
          onClose={() => setOpenExcelImportPopup(false)}
          onConfirm={(pageId: any) => createExcelImportPopup(pageId)}
          pageType={selectedExcelImportType}
        />
      )}
    </div>
  );
};

export default InitPageType;
