// 创建Excel导入类型选择对话框
const createExcelImportDialog = () => {
  return {
    type: 'dialog',
    title: '新建Excel导入',
    size: 'md',
    body: [
      {
        id: 'excel-import-form',
        type: 'form',
        title: '表单',
        mode: 'flex',
        labelAlign: 'top',
        dsType: 'api',
        feat: 'Insert',
        body: [
          {
            type: 'input-text',
            label: '页面名称',
            name: 'name',
            id: 'name-input-field',
            placeholder: '请输入页面名称',
            required: true,
            inputClassName: 'name-input',
            labelClassName: 'name-label'
          },
          {
            type: 'container',
            body: [
              {
                type: 'tpl',
                tpl: '<div style="margin: 16px 0 12px 0; font-size: 14px; font-weight: bold;">请选择页面类型：</div>'
              },
              {
                type: 'radios',
                name: 'pageType',
                required: true,
                options: [
                  {
                    label: '普通表单',
                    value: {
                      name: '新建普通表单',
                      typeNumber: 1,
                      typeName: 'form'
                    }
                  },
                  {
                    label: '流程表单',
                    value: {
                      name: '新建流程表单',
                      typeNumber: 2,
                      typeName: 'process'
                    }
                  }
                ],
                value: {
                  name: '新建普通表单',
                  typeNumber: 1,
                  typeName: 'form'
                }
              }
            ]
          }
        ],
        actions: [
          {
            type: 'button',
            label: '提交',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'submit',
                    componentId: 'excel-import-form'
                  }
                ]
              }
            },
            level: 'primary'
          }
        ],
        resetAfterSubmit: false,
        closeDialogOnSubmit: true
      }
    ],
    id: 'excel-import-dialog',
    actions: [
      {
        type: 'button',
        actionType: 'cancel',
        label: '取消',
        id: 'cancel-btn'
      },
      {
        type: 'button',
        actionType: 'confirm',
        label: '确定',
        primary: true,
        id: 'confirm-btn'
      }
    ],
    showCloseButton: false,
    closeOnOutside: false,
    closeOnEsc: false,
    showErrorMsg: true,
    showLoading: true,
    draggable: false,
    editorSetting: {
      displayName: '新建Excel导入'
    },
    themeCss: {
      dialogClassName: {
        radius: {
          'top-left-border-radius': '8px',
          'top-right-border-radius': '8px',
          'bottom-left-border-radius': '8px',
          'bottom-right-border-radius': '8px'
        }
      }
    }
  };
};

export default createExcelImportDialog;
